export const ROUTES = {
  // Auth routes (kept for password reset functionality)
  LOGIN: '/login',
  RESET_PASSWORD: '/reset-password',
  NEW_PASSWORD: (token: string) => `/reset-password/${token}`,

  // Main app routes (all protected)
  HOME: '/',
  AI_AGENTS: '/ai-agents',
  AGENT_DETAILS: (agentId: string) => `/ai-agents/${agentId}`,
  AGENT_SUITE: (suiteId: string) => `/ai-agents/suite/${suiteId}`,
  AGENT_ACTIVATION: '/ai-agents/activate',
  AGENT_ACTIVATION_SUITE: (suiteId: string) =>
    `/ai-agents/activate/suite/${suiteId}`,
  AGENT_ACTIVATION_AGENT: (agentId: string) =>
    `/ai-agents/activate/agent/${agentId}`,

  // Settings section
  SETTINGS: '/settings',
  SETTINGS_PROFILE: '/settings/profile',
  SETTINGS_NOTIFICATIONS: '/settings/notifications',
  SETTINGS_BILLING: '/settings/billing',
  SETTINGS_MEMBERS: '/settings/members',
  SETTINGS_CHANGE_EMAIL: '/settings/profile/change-email',
  SETTINGS_CHANGE_PASSWORD: '/settings/profile/change-password',
  SETTINGS_MEMBERS_INVITE: '/settings/members/invite',

  // Members section
  MEMBERS: '/members',
  MEMBERS_INVITE: '/members/invite',

  // Legacy route aliases (for backward compatibility during transition)
  DASHBOARD_BASE: '/ai-agents',
  DASHBOARD_AI_AGENTS: '/ai-agents',
  DASHBOARD_AGENT_SUITE: (suiteId: string) => `/ai-agents/suite/${suiteId}`,
  DASHBOARD_AGENT_ACTIVATION: '/ai-agents/activate',
  DASHBOARD_AGENT_ACTIVATION_SUITE: (suiteId: string) =>
    `/ai-agents/activate/suite/${suiteId}`,
  DASHBOARD_AGENT_ACTIVATION_AGENT: (agentId: string) =>
    `/ai-agents/activate/agent/${agentId}`,
  DASHBOARD_SETTINGS: '/settings',
  DASHBOARD_SETTINGS_PROFILE: '/settings/profile',
  DASHBOARD_SETTINGS_NOTIFICATIONS: '/settings/notifications',
  DASHBOARD_SETTINGS_BILLING: '/settings/billing',
  DASHBOARD_SETTINGS_MEMBERS: '/settings/members',
  DASHBOARD_SETTINGS_MEMBERS_INVITE: '/settings/members/invite',
  DASHBOARD_MEMBERS: '/members',
  DASHBOARD_MEMBERS_INVITE: '/members/invite',
} as const;

// Route paths for React Router (without leading slash for children routes)
export const ROUTE_PATHS = {
  // Auth routes
  LOGIN: 'login',
  RESET_PASSWORD: 'reset-password',
  NEW_PASSWORD: 'reset-password/:token',

  // Main app routes (for use in children arrays)
  HOME: '',
  AI_AGENTS: 'ai-agents',
  AGENT_DETAILS: 'ai-agents/:agentId',
  AGENT_SUITE: 'ai-agents/suite/:suiteId',
  AGENT_ACTIVATION: 'ai-agents/activate',
  AGENT_ACTIVATION_SUITE: 'ai-agents/activate/suite/:suiteId',
  AGENT_ACTIVATION_AGENT: 'ai-agents/activate/agent/:agentId',

  // Settings routes (for use in children arrays)
  SETTINGS: 'settings',
  SETTINGS_PROFILE: 'settings/profile',
  SETTINGS_NOTIFICATIONS: 'settings/notifications',
  SETTINGS_BILLING: 'settings/billing',
  SETTINGS_MEMBERS: 'settings/members',
  SETTINGS_MEMBERS_INVITE: 'settings/members/invite',

  // Members routes (for use in children arrays)
  MEMBERS: 'members',
  MEMBERS_INVITE: 'members/invite',
} as const;
