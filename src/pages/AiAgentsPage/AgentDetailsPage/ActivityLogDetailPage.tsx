import { Download } from 'lucide-react';
import moment from 'moment';
import React from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { Button } from '@/components/ui/Button';
import { PromptActivityModification } from '@/types/agents';

interface ActivityLogDetailState {
  activity: PromptActivityModification;
  agentName: string;
}

const ActivityLogDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { agentId } = useParams<{ agentId: string }>();
  const location = useLocation();
  const state = location.state as ActivityLogDetailState | null;

  if (!state || !state.activity) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">
            Activity Log Not Found
          </h2>
          <p className="mt-2 text-gray-600">
            The requested activity log details could not be found.
          </p>
          <Button
            onClick={() => navigate(`/ai-agents/${agentId}`)}
            className="mt-4"
          >
            Back to Agent Details
          </Button>
        </div>
      </div>
    );
  }

  const { activity, agentName } = state;

  const handleDownloadActivity = () => {
    const activityData = {
      agentName,
      dateModified: activity.dateModified,
      modifiedBy: `${activity.modifiedByFirstName} ${activity.modifiedByLastName}`,
      action: activity.action,
      timestamp: moment(activity.dateModified).format('LLLL'),
    };

    const dataStr = JSON.stringify(activityData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${agentName}_activity_${moment(activity.dateModified).format('YYYY-MM-DD_HH-mm-ss')}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Activity Log Details
            </h1>
            <p className="text-gray-600">
              Detailed information about the prompt activity
            </p>
          </div>
          <Button
            onClick={() => navigate(`/ai-agents/${agentId}`)}
            variant="outline"
          >
            Back to Agent Details
          </Button>
        </div>

        {/* Activity Details Card */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Activity Information
            </h2>
            <Button onClick={handleDownloadActivity} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Details
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Agent Name
              </label>
              <p className="mt-1 text-sm text-gray-900">{agentName}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Action
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {activity.action.charAt(0).toUpperCase() + activity.action.slice(1).toLowerCase()}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Modified By
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {activity.modifiedByFirstName} {activity.modifiedByLastName}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                User ID
              </label>
              <p className="mt-1 text-sm text-gray-900 font-mono">
                {activity.modifiedBy}
              </p>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">
                Date & Time
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {moment(activity.dateModified).format('LLLL')}
              </p>
              <p className="mt-1 text-xs text-gray-500">
                ({moment(activity.dateModified).fromNow()})
              </p>
            </div>
          </div>
        </div>

        {/* Timeline Visualization */}
        <div className="mt-8 rounded-lg bg-white p-6 shadow-sm">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Activity Timeline
          </h3>
          <div className="flex items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
              <span className="text-sm font-medium">1</span>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-900">
                Prompt {activity.action.toLowerCase()}
              </p>
              <p className="text-sm text-gray-500">
                by {activity.modifiedByFirstName} {activity.modifiedByLastName}
              </p>
              <p className="text-xs text-gray-400">
                {moment(activity.dateModified).format('MMM DD, YYYY [at] h:mm A')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityLogDetailPage;
