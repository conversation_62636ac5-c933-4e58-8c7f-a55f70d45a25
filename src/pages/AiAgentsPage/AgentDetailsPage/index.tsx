import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import AgentsDropdown from '@/components/ui/AgentsDropdown';
import { useTenant } from '@/context/TenantContext';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import {
  useAgentPromptsApi,
  usePromptActivityLogApi,
} from '@/services/upivotalAgenticService';
import {
  AgentPrompt,
  AIAgent,
  PromptActivityModification,
} from '@/types/agents';
import { UserBasicInfoPayload } from '@/types/user';

import AgentInfo from './AgentInfo';
import AgentPrompts from './AgentPrompts';

type TabType = 'Agent Info' | 'Agent Prompts';

const AgentDetails = () => {
  const location = useLocation();
  const locationState = location.state as {
    selectedAgent?: AIAgent;
  } | null;

  const { agentId } = useParams<{ agentId: string }>();
  const { setActiveAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  // Get all agents from all suites
  const allAgents =
    userData?.userInfo?.tenant?.claimedAgentSuites?.flatMap(suite =>
      suite.suite.availableAgents.map(agent => ({
        ...agent,
        suiteKey: suite.suite.agentSuiteKey,
      }))
    ) || [];

  const [activeTab, setActiveTab] = useState<TabType>('Agent Info');
  const tabs: TabType[] = ['Agent Info', 'Agent Prompts'];

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  const [selectedAgent, setSelectedAgent] = useState<string>(agentId!);
  const [currentAgent, setCurrentAgent] = useState<AIAgent | null>(null);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] = useState(false);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  // Prompt management state
  const [agentPrompts, setAgentPrompts] = useState<AgentPrompt[]>([]);
  const [promptActivityLog, setPromptActivityLog] = useState<
    PromptActivityModification[]
  >([]);
  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false);
  const [isLoadingActivityLog, setIsLoadingActivityLog] = useState(false);

  // API hooks
  const { getAgentPrompts } = useAgentPromptsApi();
  const { getPromptActivityLog } = usePromptActivityLogApi();

  // Function to fetch prompt data for the current agent
  const fetchPromptData = async (agentKey: string) => {
    try {
      // Fetch prompts and activity log in parallel
      setIsLoadingPrompts(true);
      setIsLoadingActivityLog(true);

      const [promptsResponse, activityLogResponse] = await Promise.all([
        getAgentPrompts(agentKey),
        getPromptActivityLog(agentKey),
      ]);

      if (promptsResponse.status) {
        setAgentPrompts(promptsResponse.data);
      }

      if (activityLogResponse.status) {
        setPromptActivityLog(activityLogResponse.data.modifications);
      }
    } catch (error) {
      console.error('Failed to fetch prompt data:', error);
    } finally {
      setIsLoadingPrompts(false);
      setIsLoadingActivityLog(false);
    }
  };

  useEffect(() => {
    const switchAgent = async () => {
      if (locationState?.selectedAgent) {
        try {
          // await setActiveAgent(locationState.selectedAgent);
          setCurrentAgent(locationState.selectedAgent);
          // Fetch prompt data for the selected agent
          await fetchPromptData(locationState.selectedAgent.agentKey);
        } catch (error) {
          console.error('Failed to switch agent:', error);
        }
      }
    };

    switchAgent();
  }, [locationState?.selectedAgent, setActiveAgent]);

  // Handle agent selection change
  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setIsAgentDropdownOpen(false);
  };

  return (
    <div className="flex h-full">
      {/* Chat Interface - LHS */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        // externalMessage={locationState?.userMessage}
      />

      {/* Main Content - RHS */}
      <div className="flex w-full max-w-[750px] flex-1 flex-col overflow-y-auto p-8">
        <div className="relative w-fit" ref={agentDropdownRef}>
          <AgentsDropdown
            isOpen={isAgentDropdownOpen}
            onToggle={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
            currentItem={allAgents
              .map(a => ({
                id: a.agentKey,
                name: a.agentName,
                icon: a.avatar,
              }))
              .find(a => a.id === selectedAgent)}
            options={allAgents.map(a => ({
              id: a.agentKey,
              name: a.agentName,
              icon: a.avatar,
            }))}
            onItemSelect={agent => handleAgentChange(agent.id)}
            placeholder="Agent"
            noOptionsMessage="No other agents available"
          />
        </div>
        {/* Header */}
        <div className="my-6">
          {/* Tabs */}
          <div className="flex space-x-1 border-b border-gray-200">
            {tabs.map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={clsx(
                  'px-4 py-2 font-spartan text-sm font-medium transition-colors',
                  activeTab === tab
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-gray-600 hover:text-blackOne'
                )}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div>
          {activeTab === 'Agent Info' && (
            <AgentInfo currentAgent={currentAgent!} />
          )}

          {activeTab === 'Agent Prompts' && (
            <AgentPrompts
              currentAgent={currentAgent!}
              agentPrompts={agentPrompts}
              promptActivityLog={promptActivityLog}
              isLoadingPrompts={isLoadingPrompts}
              isLoadingActivityLog={isLoadingActivityLog}
              onPromptUpdate={() =>
                fetchPromptData(currentAgent?.agentKey || '')
              }
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentDetails;
