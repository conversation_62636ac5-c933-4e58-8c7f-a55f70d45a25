import 'react-quill/dist/quill.snow.css';

// import parse from 'html-react-parser';
import { Download, PencilLine } from 'lucide-react';
import moment from 'moment';
import { useState } from 'react';
import ReactQuill from 'react-quill';

import DoneOrCancelWidget from '@/components/ui/DoneOrCancelWidget';
import { AgentInfoProps } from '@/types/agents';

interface Activity {
  action: string;
  dateModified: string;
  modifiedBy: string;
  modifiedByFirstName: string;
  modifiedByLastName: string;
}

const AgentPrompts: React.FC<AgentInfoProps> = ({ currentAgent }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [activityLog, setActivityLog] = useState<Activity[]>([]);
  const [showAllActivities, setShowAllActivities] = useState(false);

  const handleUpdatePrompt = (value: string) => {
    // console.log('prompt', parse(value)?.props?.children);
    console.log('prompt', value);
    // updateTask(
    //   {
    //     prompt: value,
    //     taskRef: taskRef || '',
    //   },
    //   {
    //     onSuccess: () => setReadOnly(false),
    //   }
    // );
  };

  return (
    <div className="mt-4">
      <div className="w-full rounded-[10px] border border-grayTwentyEight bg-lightOrangeTwo px-4 py-1.5 capitalize">
        <div className="flex w-full items-center justify-between">
          <div>
            <p className="py-1 text-lg font-medium text-[#0F0006]">
              {currentAgent?.agentName} Prompt Update
            </p>
            <p className="text-xs">
              Last Updated: {moment(Date.now()).format('lll')}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {!isEditing ? (
              <button
                onClick={e => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
                className="flex items-center gap-2 rounded-full border border-grayTen px-4 py-2 text-sm transition-colors hover:border-primary hover:bg-red-50 hover:text-primary"
              >
                Edit
                <PencilLine className="h-4 w-4" />
              </button>
            ) : (
              <button
                onClick={e => {
                  e.stopPropagation();
                  setIsEditing(false);
                  handleUpdatePrompt(prompt);
                }}
                className="flex items-center gap-2 rounded-full border border-[#3E8E58] bg-[#3E8E58] px-4 py-2 text-sm text-white transition-colors hover:border-[#2ead59] hover:bg-[#2ead59]"
              >
                Publish
                <PencilLine className="h-4 w-4" />
              </button>
            )}

            <button
              // onClick={handleDownload}
              onClick={() => {}}
              className="flex items-center gap-2 rounded-full border border-grayTen px-4 py-2 text-sm transition-colors hover:border-primary hover:bg-red-50 hover:text-primary"
            >
              Download
              <Download className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="relative mb-10 pb-2 pt-6">
          <ReactQuill
            theme="snow"
            defaultValue={currentAgent?.description}
            onChange={setPrompt}
            readOnly={!isEditing}
          />
          {isEditing && (
            <div className="absolute -bottom-9 right-0 flex gap-x-1">
              <DoneOrCancelWidget
                done={() => {
                  setIsEditing(false);
                  handleUpdatePrompt(prompt);
                }}
                cancel={() => setIsEditing(false)}
                // isDoneLoading={isTaskUpdating}
                isDoneLoading={false}
              />
            </div>
          )}
        </div>
      </div>

      {/* Prompt Activity Log Section */}
      <div className="mt-4 w-full bg-lightOrangeTwo px-4 py-1.5 capitalize">
        <p className="py-2 text-lg font-medium text-[#0F0006]">Activity Log</p>
        {activityLog.length > 0 ? (
          <div className="space-y-3">
            {(showAllActivities ? activityLog : activityLog.slice(0, 3)).map(
              (activity, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        fill="white"
                      />
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        stroke="#FF3E00"
                        strokeWidth="11"
                      />
                    </svg>

                    <div>
                      <div className="mb-1.5 text-sm text-gray-400">
                        {moment(activity.dateModified).format('LLL')}
                      </div>
                      <div className="text-sm">{`${activity.modifiedByFirstName ?? ''} ${activity.modifiedByLastName ?? ''} ${activity.action.toLocaleLowerCase()} the document`}</div>
                    </div>
                  </div>

                  <div className="text-primary underline">View</div>
                </div>
              )
            )}

            {/* Load More Button */}
            {activityLog.length > 3 && !showAllActivities && (
              <div className="flex justify-center pt-3">
                <button
                  onClick={() => setShowAllActivities(true)}
                  className="rounded-md border border-primary px-4 py-2 text-sm font-medium text-primary transition-colors hover:bg-primary hover:text-white"
                >
                  Load more
                </button>
              </div>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-400">No activity log available</p>
        )}
      </div>
    </div>
  );
};

export default AgentPrompts;
