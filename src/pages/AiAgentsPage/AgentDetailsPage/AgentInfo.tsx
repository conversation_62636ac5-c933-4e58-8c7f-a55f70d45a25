import moment from 'moment';

import { agentSuites as mockAgents, featureIcons } from '@/data/constants';
import { AgentInfoProps } from '@/types/agents';

const AgentInfo: React.FC<AgentInfoProps> = ({ currentAgent }) => {
  return (
    <div>
      <div className="w-full bg-lightOrangeTwo px-4 py-1.5 capitalize">
        <p className="py-1 text-lg font-medium text-[#0F0006]">
          {currentAgent?.agentName} Details
        </p>
        <p className="text-xs">
          Last Updated: {moment(Date.now()).format('lll')}
        </p>
      </div>

      <div className="mt-4 rounded border border-grayTwentyEight p-4">
        <p className="mb-4 font-semibold">Agent Title</p>
        <div className="flex items-center gap-2 py-1">
          <div className="h-12 min-w-12 rounded-full bg-peachTwo">
            <img
              key={currentAgent?.agent<PERSON>ey}
              src={currentAgent?.avatar}
              className="h-12 w-12 rounded-full object-cover"
              alt={currentAgent?.agentName}
              onError={e => {
                const fallbackAgent = mockAgents.find(
                  agent =>
                    agent.id.toLowerCase() ===
                    currentAgent?.agentKey.toLowerCase()
                );
                if (fallbackAgent) {
                  (e.target as HTMLImageElement).src = fallbackAgent.image;
                }
              }}
            />
          </div>

          <div>
            <p className="text-darkGray">
              Hi, I'm{' '}
              <span className="font-semibold">
                {`${currentAgent?.agentName} — ${currentAgent?.description}`}
              </span>{' '}
            </p>
            <p>{currentAgent?.roleDescription}</p>
          </div>
        </div>
      </div>

      <div className="mt-4 rounded border border-grayTwentyEight p-4">
        <p className="mb-4 font-semibold">Description</p>
        <div className="flex flex-col gap-2">
          {currentAgent?.roles.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <img src={featureIcons[0]} alt="" className="w-3 md:w-4" />
              <p className="text-sm text-blackTwo">{feature}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AgentInfo;
