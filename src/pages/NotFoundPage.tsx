import { ArrowLeft, Home } from 'lucide-react';
import React from 'react';
import { useNavigate } from 'react-router-dom';

export const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/ai-agents');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="flex min-h-[calc(100vh-200px)] items-center justify-center bg-gray-50">
      <div className="w-full max-w-md text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="mb-4 text-5xl font-bold text-primary sm:text-8xl">
            404
          </div>
          <div className="mx-auto mb-6 h-1 w-24 bg-primary"></div>
        </div>

        {/* Content */}
        <div className="mb-8">
          <h1 className="mb-4 font-semibold text-blackOne sm:text-xl">
            Page Not Found
          </h1>
          <p className="mb-6 text-xs text-subText sm:text-sm">
            Sorry, the page you are looking for doesn't exist or has been moved.
            Please check the URL or navigate back to continue.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleGoHome}
            className="flex w-full items-center justify-center rounded-lg bg-primary px-6 py-3 text-white transition-colors hover:bg-primary/90"
          >
            <Home className="mr-2 h-4 w-4" />
            Go to Agents Hub
          </button>

          <button
            onClick={handleGoBack}
            className="flex w-full items-center justify-center rounded-lg border border-gray-300 px-6 py-3 text-gray-700 transition-colors hover:bg-gray-50"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </button>
        </div>

        {/* Additional Help */}
        <div className="mt-8 text-sm text-subText">
          <p>Need help? Contact support or try one of these popular pages:</p>
          <div className="mt-2 flex justify-center space-x-4">
            <button
              onClick={() => navigate('/ai-agents')}
              className="text-primary hover:underline"
            >
              AI Agents
            </button>
            <button
              onClick={() => navigate('/members')}
              className="text-primary hover:underline"
            >
              Members
            </button>
            <button
              onClick={() => navigate('/settings')}
              className="text-primary hover:underline"
            >
              Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
