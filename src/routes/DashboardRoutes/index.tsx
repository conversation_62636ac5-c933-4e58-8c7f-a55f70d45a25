import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { retryChunkLoad } from '@/utils/chunkErrorHandler';

const AIAgentsDashboardPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/AiAgentsPage')))
);

const AgentDetailsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/AiAgentsPage/AgentDetailsPage'))
  )
);

const ActivityLogDetailPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () =>
        import(
          '../../pages/AiAgentsPage/AgentDetailsPage/ActivityLogDetailPage'
        )
    )
  )
);

const SettingsPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/SettingsPage')))
);

const MembersSettings = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/SettingsPage/MembersSettings'))
  )
);

const InviteMembersPage = withSuspense(
  lazy(() =>
    retryChunkLoad(
      () => import('../../pages/SettingsPage/MembersSettings/InviteMembersPage')
    )
  )
);

const NotFoundPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/NotFoundPage')))
);

export const DashboardRoutes = [
  {
    index: true,
    element: <Navigate to="ai-agents" replace />,
  },
  {
    path: 'ai-agents',
    element: <AIAgentsDashboardPage />,
  },
  {
    path: 'ai-agents/:agentId',
    element: <AgentDetailsPage />,
  },
  {
    path: 'ai-agents/:agentId/activity-log',
    element: <ActivityLogDetailPage />,
  },
  {
    path: 'settings/*',
    element: <SettingsPage />,
  },
  {
    path: 'members',
    element: <MembersSettings />,
  },
  {
    path: 'members/invite',
    element: <InviteMembersPage />,
  },
  {
    path: '*',
    element: <NotFoundPage />,
  },
];
