import { useEffect, useLayoutEffect, useRef, useState } from 'react';

import { scyra } from '../../assets/images';
import { ConversationState } from '../../types/agents';
import { ChatInput } from './ChatInput';
import { ChatMessage } from './ChatMessage';
import { ProceedToDashboardButton } from './ProceedToDashboardButton';
import { SignupButton } from './SignupButton';
import { TypingIndicator } from './TypingIndicator';

interface ConversationalChatInterfaceProps {
  state: ConversationState;
  sendMessage: (userMessage: string) => Promise<void>;
}

export const ConversationalChatInterface = ({
  state,
  sendMessage,
}: ConversationalChatInterfaceProps) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  // Track user scroll position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => container.removeEventListener('scroll', handleScroll);
  }, [state.messages.length]);

  // Set initial scroll position to bottom BEFORE first paint
  useLayoutEffect(() => {
    const container = messagesContainerRef.current;
    if (container && state.messages.length > 0) {
      // Immediately set scroll to bottom without animation
      container.scrollTop = container.scrollHeight;
    }
  }, [state.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom AND this is not the initial load
    if (isUserAtBottom && state.messages.length > 0) {
      scrollToBottom();
    }
  }, [state.messages, isUserAtBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!state.isLoading && isUserAtBottom && state.messages.length > 0) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [state.isLoading, isUserAtBottom]);

  const handleProceedToDashboard = () => {
    // Navigate to main application
    window.location.href = '/ai-agents';
  };

  const getLastScyraMessage = () => {
    const scyraMessages = state.messages.filter(msg => msg.sender === 'scyra');
    return scyraMessages[scyraMessages.length - 1];
  };

  const lastScyraMessage = getLastScyraMessage();

  return (
    <div className="flex h-[95%] flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-4"
      >
        {state.messages.map(message => (
          <div key={message.id}>
            <ChatMessage message={message} />

            {/* Show signup button only after the most recent Scyra message */}
            {message.sender === 'scyra' &&
              message.id === lastScyraMessage?.id &&
              state.showSignupButton && (
                <div className="ml-[52px]">
                  <SignupButton disabled={state.isLoading} />
                </div>
              )}

            {/* Show proceed to Dashboard button after signup completion */}
            {message.sender === 'scyra' &&
              message.id === lastScyraMessage?.id &&
              state.showProceedToDashboardButton && (
                <div className="ml-12">
                  <ProceedToDashboardButton
                    onProceedToDashboard={handleProceedToDashboard}
                    disabled={state.isLoading}
                  />
                </div>
              )}
          </div>
        ))}

        {/* Typing Indicator */}
        {state.isLoading && (
          <TypingIndicator agentImageSrc={scyra} agentName="Scyra" />
        )}
      </div>

      {/* Chat Input */}
      <div className="px-4 py-4">
        <ChatInput onSendMessage={sendMessage} disabled={state.isLoading} />
      </div>
    </div>
  );
};
