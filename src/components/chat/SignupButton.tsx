import { Mail } from 'lucide-react';

interface SignupButtonProps {
  disabled?: boolean;
}

export const SignupButton = ({
  // onSignupStart,
  disabled = false,
}: SignupButtonProps) => {
  // Signup functionality removed for admin app
  // This component is kept for backward compatibility but does nothing
  return (
    <div className="mt-3 flex justify-start gap-2 text-gray-600">
      <button
        disabled={true}
        className="flex cursor-not-allowed items-center gap-2 rounded-md border border-gray-400 bg-white px-3 py-1 text-sm font-medium opacity-50"
      >
        <Mail className="w-4" />
        <span>Contact Admin</span>
      </button>

      {/* <button
        // onClick={onSignupStart}
        disabled={disabled}
        className="flex gap-2 items-center px-3 py-1 text-sm font-medium bg-white rounded-md border border-gray-400 transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <FolderOpen className="w-4" />
        <span>Connect CRM</span>
      </button>

      <button
        // onClick={onSignupStart}
        disabled={disabled}
        className="flex gap-2 items-center px-3 py-1 text-sm font-medium bg-white rounded-md border border-gray-400 transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <Mail className="w-4" />
        <span>Connect Email</span>
      </button>

      <button
        // onClick={onSignupStart}
        disabled={disabled}
        className="flex gap-2 items-center px-3 py-1 text-sm font-medium bg-white rounded-md border border-gray-400 transition-colors hover:border-primary hover:bg-lightOrangeTwo disabled:cursor-not-allowed disabled:opacity-50"
      >
        <Mail className="w-4" />
        <span>Connect SMS Provider</span>
      </button> */}
    </div>
  );
};
