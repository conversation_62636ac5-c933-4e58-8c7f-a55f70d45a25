import React from 'react';

import { cn } from '@/lib/twMerge/cn';

type AlertWidth = 'full' | 'sm' | 'md' | 'lg' | 'xl';

interface AlertBannerProps {
  message: string;
  type: 'error' | 'success' | 'warning';
  onClose: () => void;
  showIcon?: boolean;
  width?: AlertWidth;
}

const AlertBanner: React.FC<AlertBannerProps> = ({
  message,
  type,
  onClose,
  showIcon = true,
  width = 'full',
}) => {
  const [isHovered, setIsHovered] = React.useState(false);
  const isError = type === 'error';
  const isWarning = type === 'warning';
  const bgColor = isError
    ? 'bg-[#CE1111]'
    : isWarning
      ? 'bg-[#F88138]'
      : 'bg-[#1BB52B]';

  const widthClasses: Record<AlertWidth, string> = {
    full: 'w-full',
    sm: 'w-full max-w-sm',
    md: 'w-full max-w-md',
    lg: 'w-full max-w-[662px]',
    xl: 'w-full max-w-xl',
  };

  const icon = isError ? (
    <svg className="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
        clipRule="evenodd"
      />
    </svg>
  ) : isWarning ? (
    <svg className="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
        clipRule="evenodd"
      />
    </svg>
  ) : (
    <svg className="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
        clipRule="evenodd"
      />
    </svg>
  );

  return (
    <div className="absolute left-0 right-0 top-0 z-50 flex animate-slide-down justify-center">
      <div
        className={cn(
          'mx-4 mt-4 flex items-center justify-between rounded-lg px-6 py-4 text-white shadow-lg',
          bgColor,
          widthClasses[width]
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-center text-sm">
          {icon && showIcon && icon}
          <span className="text-sm font-normal text-white">{message}</span>
        </div>
        <button onClick={onClose} className="text-white hover:text-gray-200">
          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default AlertBanner;
