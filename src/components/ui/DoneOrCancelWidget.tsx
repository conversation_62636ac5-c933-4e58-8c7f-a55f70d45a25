import { Check, X } from 'lucide-react';

type Props = {
  cancel: () => void;
  done: () => void;
  isDoneLoading: boolean;
};

export default function DoneOrCancelWidget({
  cancel,
  done,
  isDoneLoading,
}: Props) {
  return (
    <>
      <button
        type="button"
        disabled={isDoneLoading}
        className="flex h-8 w-8 items-center justify-center rounded-[2px] border border-primary bg-lightOrangeTwo text-primary transition-colors hover:bg-primary hover:text-white disabled:bg-gray-5"
      >
        <Check onClick={() => done()} className="cursor-pointer" />
      </button>
      <button
        type="button"
        className="flex h-8 w-8 items-center justify-center rounded-[2px] border border-primary bg-lightOrangeTwo text-primary transition-colors hover:bg-primary hover:text-white"
      >
        <X onClick={() => cancel()} className="cursor-pointer" />
      </button>
    </>
  );
}
